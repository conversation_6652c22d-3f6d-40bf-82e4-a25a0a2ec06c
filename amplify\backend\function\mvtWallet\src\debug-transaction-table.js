/**
 * Debug script to check MVTWalletTransaction table existence and test transaction creation
 */

// Load environment variables from .env file
require('dotenv').config({ path: '../.env' });

const AWS = require('aws-sdk');
const { getTableName, tableExists } = require('./shared/database/dynamoUtils');
const { createMVTWalletTransaction } = require('./modules/transaction/transaction.service');
const { TRANSACTION_TYPES, TOKEN_TYPES, TRANSACTION_STATUS } = require('./shared/constants');

// Configure AWS
AWS.config.update({
  region: process.env.AWS_REGION || 'us-east-1'
});

const ddb = new AWS.DynamoDB();

async function debugTransactionTable() {
  console.log('🔍 Debugging MVTWalletTransaction table...\n');

  // Check environment variables
  console.log('📋 Environment Variables:');
  console.log('- API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT:', process.env.API_MYVILLAGEPROJECTADMI_GRAPHQLAPIIDOUTPUT);
  console.log('- ENV:', process.env.ENV);
  console.log('- AWS_REGION:', process.env.AWS_REGION);
  console.log('');

  // Generate table name
  const tableName = getTableName("MVTWalletTransaction");
  console.log('🏷️  Generated table name:', tableName);
  console.log('');

  // Check if table exists
  console.log('🔍 Checking if table exists...');
  try {
    const exists = await tableExists(ddb, tableName);
    console.log('✅ Table exists:', exists);
    
    if (exists) {
      // Get table description
      try {
        const tableDescription = await ddb.describeTable({ TableName: tableName }).promise();
        console.log('📊 Table status:', tableDescription.Table.TableStatus);
        console.log('📊 Item count:', tableDescription.Table.ItemCount);
        console.log('📊 Table size:', tableDescription.Table.TableSizeBytes, 'bytes');
      } catch (error) {
        console.log('❌ Error getting table description:', error.message);
      }
    } else {
      console.log('❌ Table does not exist!');
      
      // Try to list all tables to see what's available
      try {
        const allTables = await ddb.listTables().promise();
        console.log('📋 Available tables:');
        allTables.TableNames.forEach(name => {
          if (name.includes('MVTWallet') || name.includes('Transaction')) {
            console.log('  - 🎯', name);
          } else {
            console.log('  -', name);
          }
        });
      } catch (error) {
        console.log('❌ Error listing tables:', error.message);
      }
    }
  } catch (error) {
    console.log('❌ Error checking table existence:', error.message);
  }

  console.log('');

  // Test transaction creation
  console.log('🧪 Testing transaction creation...');
  try {
    const testTransactionData = {
      id: `test-usdc-deposit-${Date.now()}`,
      transactionType: TRANSACTION_TYPES.USDC_DEPOSIT,
      tokenType: TOKEN_TYPES.USDC,
      amount: 100.50,
      fromWalletId: "admin-wallet-test",
      toWalletId: "usdc-liquidity-pool",
      fromUserId: null,
      toUserId: null,
      status: TRANSACTION_STATUS.COMPLETED,
      transactionHash: "0x1234567890abcdef",
      internalTxId: `test-internal-${Date.now()}`,
      description: "Test USDC deposit for debugging",
      adminUserId: "test-admin-123",
      gasUsed: 21000,
      blockNumber: "12345678",
      metadata: {
        operation: "usdc_deposit",
        test: true,
        debugTimestamp: new Date().toISOString()
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    console.log('📝 Test transaction data:', JSON.stringify(testTransactionData, null, 2));
    console.log('');

    const result = await createMVTWalletTransaction(testTransactionData);
    console.log('✅ Transaction creation result:', result);
    
    if (result) {
      console.log('🎉 Transaction created successfully!');
    } else {
      console.log('⚠️  Transaction creation returned false (likely table doesn\'t exist)');
    }
  } catch (error) {
    console.log('❌ Error creating test transaction:', error.message);
    console.log('Stack trace:', error.stack);
  }

  console.log('\n🏁 Debug complete!');
}

// Run the debug if this file is executed directly
if (require.main === module) {
  debugTransactionTable().catch(console.error);
}

module.exports = { debugTransactionTable };
